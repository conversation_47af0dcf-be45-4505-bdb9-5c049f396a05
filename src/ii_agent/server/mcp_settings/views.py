"""MCP settings management API endpoints."""

import json
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional

from ii_agent.db.models import User
from ii_agent.server.api.deps import SessionDep, get_db_session, DBSession
from ii_agent.server.auth.middleware import CurrentUser, get_current_user
from ii_agent.server.mcp_settings.models import (
    CodexMetadata,
    CodexConfigConfigure,
    MCPSettingCreate,
    MCPSettingUpdate,
    MCPSettingInfo,
    MCPSettingList,
    MCPServersConfig,
)
from ii_agent.server.mcp_settings.service import (
    create_mcp_settings,
    update_mcp_settings,
    get_mcp_settings,
    list_mcp_settings,
    delete_mcp_settings,
)


router = APIRouter(prefix="/user-settings/mcp", tags=["User MCP Settings Management"])



# TODO: Change this to something like /{tool_type} and dynamically choose type, when we have more specific tools
@router.get("/codex", response_model=Optional[MCPSettingInfo])
async def get_codex_settings(
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """
    Get current Codex MCP settings for the user.
    Returns None if no Codex settings exist.
    """

    # Get all MCP settings for the user
    existing_settings = await list_mcp_settings(
        db_session=db,
        user_id=str(current_user.id),
        only_active=False,
    )

    # Find Codex-specific settings
    for setting in existing_settings.settings:
        if setting.metadata and isinstance(setting.metadata, CodexMetadata):
            return setting

    return None


# TODO: Change this to something like /{tool_type} and dynamically choose type, when we have more specific tools
@router.post("/codex", response_model=MCPSettingInfo)
async def configure_codex_mcp(
    request: CodexConfigConfigure,
    current_user: CurrentUser,
    db: SessionDep,
):
    """
    Configure Codex MCP with authentication.
    This endpoint handles the specific configuration for Codex MCP tool.
    """

    # Validate auth_json has required fields
    auth_json = request.auth_json
    apikey = request.apikey
    model = request.model

    if not auth_json and not apikey:
        raise HTTPException(status_code=400, detail="Authentication JSON or API Key is required")
    elif not auth_json and apikey:
        auth_json = {
            "OPENAI_API_KEY": apikey
        }
    elif auth_json and apikey:
        auth_json["OPENAI_API_KEY"] = apikey

    # If using API Key, use gpt-5 by default
    if (apikey or auth_json.get("OPENAI_API_KEY", None)) and model == "gpt-5-codex":
        raise HTTPException(status_code=400, detail="gpt-5-codex is not yet supported for API Key")

    mcp_json_config = {
        "mcpServers": {
                "codex-as-mcp": {
                "command": "uvx",
                "args": [
                    "--from",
                    "git+https://github.com/Intelligent-Internet/codex-as-mcp.git@main",
                    "codex-as-mcp",
                    "--yolo",
                    f"--model={model if model else 'gpt-5-codex'}",
                ]
            }
        }
    }
    mcp_config = MCPServersConfig.model_validate(mcp_json_config)
    store_path = "/root/.codex/auth.json"

    # Prepare metadata with auth info and server-controlled store path
    # Generalize this if we have more metadata type, validate from the tool_type sent from frontend
    metadata = CodexMetadata(
        auth_json=auth_json, #pyright: ignore 
        store_path=store_path,
    )

    # Check if there's an existing Codex configuration
    existing_settings = await list_mcp_settings(
        db_session=db,
        user_id=str(current_user.id),
        only_active=False
    )

    codex_setting = None
    for setting in existing_settings.settings:
        if setting.metadata and isinstance(setting.metadata, CodexMetadata):
            codex_setting = setting
            break

    if codex_setting:
        # Update existing Codex configuration
        result = await update_mcp_settings(
            db_session=db,
            setting_id=codex_setting.id,
            setting_update=MCPSettingUpdate(
                mcp_config=mcp_config,
                metadata=metadata,
                is_active=True
            ),
            user_id=str(current_user.id),
        )
    else:
        # Create new Codex configuration
        result = await create_mcp_settings(
            db_session=db,
            mcp_setting_in=MCPSettingCreate(
                mcp_config=mcp_config,
                metadata=metadata,
            ),
            user_id=str(current_user.id),
        )

    return result

@router.post("/", response_model=MCPSettingInfo)
async def create_mcp_setting(
    setting: MCPSettingCreate,
    current_user: CurrentUser,
    db: SessionDep,
):
    """Create new MCP settings for the current user. This will deactivate any existing active settings."""

    result = await create_mcp_settings(
        db_session=db,
        mcp_setting_in=setting,
        user_id=str(current_user.id),
    )

    return result


@router.get("/", response_model=MCPSettingList)
async def list_user_mcp_settings(
    only_active: bool = False,
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """List all MCP settings for the current user."""

    return await list_mcp_settings(
        db_session=db,
        user_id=str(current_user.id),
        only_active=only_active,
        no_metadata=True
    )


@router.get("/{setting_id}", response_model=MCPSettingInfo)
async def get_mcp_setting(
    setting_id: str,
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """Get specific MCP settings by ID."""

    mcp_setting = await get_mcp_settings(
        db_session=db,
        setting_id=setting_id,
        user_id=str(current_user.id),
    )

    if not mcp_setting:
        raise HTTPException(status_code=404, detail="MCP settings not found")

    return mcp_setting


@router.put("/{setting_id}", response_model=MCPSettingInfo)
async def update_mcp_setting(
    setting_id: str,
    setting_update: MCPSettingUpdate,
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """Update existing MCP settings."""

    updated_setting = await update_mcp_settings(
        db_session=db,
        setting_id=setting_id,
        setting_update=setting_update,
        user_id=str(current_user.id),
    )

    if not updated_setting:
        raise HTTPException(status_code=404, detail="MCP settings not found")

    return updated_setting


@router.delete("/{setting_id}")
async def delete_mcp_setting(
    setting_id: str,
    current_user: User = Depends(get_current_user),
    db: DBSession = Depends(get_db_session),
):
    """Delete MCP settings by ID."""

    deleted = await delete_mcp_settings(
        db_session=db,
        setting_id=setting_id,
        user_id=str(current_user.id),
    )

    if not deleted:
        raise HTTPException(status_code=404, detail="MCP settings not found")

    return {"message": "MCP settings deleted successfully"}
