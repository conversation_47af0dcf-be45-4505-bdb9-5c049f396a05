from typing import Optional, Literal

from .base import BaseWebVisitClient
from .firecrawl import FireCrawlWebVisitClient
from .gemini import GeminiWebVisitClient
from .config import WebVisitConfig, CompressorConfig

def create_web_visit_client(settings: WebVisitConfig, compressor_config: CompressorConfig, client_type: Optional[Literal["firecrawl", "gemini"]] = None) -> BaseWebVisitClient:
    """
    Factory function that creates a web visit client based on available API keys.
    Priority order: FireCrawl > Gemini > Jina > Tavily > Markdownify

    Args:
        settings: Settings object containing API keys

    Returns:
        BaseWebVisitClient: An instance of a web visit client
    """
    firecrawl_key = settings.firecrawl_api_key
    gemini_key = settings.gemini_api_key

    if (firecrawl_key and not client_type) or (client_type == "firecrawl"):
        print("Using FireCrawl to visit webpage")
        if not firecrawl_key:
            raise ValueError("FireCrawl API key not found")
        return FireCrawlWebVisitClient(api_key=firecrawl_key, compressor_config=compressor_config)
    
    if (gemini_key and not client_type) or (client_type == "gemini"):
        print("Using Gemini to visit webpage")
        if not gemini_key:
            raise ValueError("Gemini API key not found")
        return GeminiWebVisitClient(api_key=gemini_key)

    raise ValueError("No web visit API key found")