from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator, Optional, List
import uuid
from sqlalchemy import asc, select, desc, func
from sqlalchemy.orm import selectinload
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.ext.asyncio import async_sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession as DBSession
from ii_tool.integrations.app.config import config
# Use the same db schema without duplication code
from ii_agent.db.models import User, APIKey


engine = create_async_engine(config.database_url, echo=False, future=True)
SessionLocal = async_sessionmaker(bind=engine, expire_on_commit=False)


@asynccontextmanager
async def get_db() -> AsyncGenerator[DBSession, None]:
    """Get a database session as a context manager.

    Yields:
        A database session that will be automatically committed or rolled back
    """
    async with SessionLocal() as db:
        try:
            yield db
            await db.commit()
        except Exception:
            await db.rollback()
            raise
        finally:
            await db.close()


async def get_user_by_api_key(api_key: str) -> User | None:
    """Resolve a user from their API key."""
    async with get_db() as db:
        result = await db.execute(
            select(APIKey)
            .options(selectinload(APIKey.user))
            .where(
                APIKey.api_key == api_key,
                APIKey.is_active.is_(True),
            )
        )
        api_key_obj = result.scalar_one_or_none()

        if not api_key_obj or not api_key_obj.user or not api_key_obj.user.is_active:
            return None

        return api_key_obj.user


