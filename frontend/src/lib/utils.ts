import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}

export const parseJson = (jsonString: string) => {
    try {
        return JSON.parse(jsonString)
    } catch {
        return null
    }
}

export const getFirstCharacters = (str: string) => {
    return str
        .trim()
        .split(/\s+/)
        .map((word) => word.charAt(0).toUpperCase())
        .join('')
}

export const extractUrls = (markdown: string) => {
    const urlRegex = /\[.*?\]\((https?:\/\/[^\s)]+)\)|(https?:\/\/[^\s)]+)/g

    const urls: string[] = []
    let match: RegExpExecArray | null

    while ((match = urlRegex.exec(markdown)) !== null) {
        let url = match[1] || match[2]
        if (url) {
            // Remove trailing markdown punctuation like **, _, ., ,, ), etc.
            url = url
                .replace(/[*_]+$/g, '')
                .replace(/[.,)]+$/g, '')
                .replace(/[*_.,!?`)+]+$/g, '')
            urls.push(url)
        }
    }

    return urls
}

export const isImageFile = (fileName: string): boolean => {
    const ext = fileName.split('.').pop()?.toLowerCase() || ''
    return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'heic', 'svg'].includes(
        ext
    )
}

export const isE2bLink = (url: string): boolean => {
    try {
        const parsed = new URL(url)
        return (
            parsed.hostname.includes('e2b') || parsed.hostname.includes('e2b-')
        )
    } catch {
        return false
    }
}
