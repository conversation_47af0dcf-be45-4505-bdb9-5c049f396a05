export enum ActionType {
  ADD = "add",
  DELETE = "delete",
  UPDATE = "update",
}

export interface FileChange {
  type: ActionType;
  oldContent?: string;
  newContent?: string;
  movePath?: string | null;
}

export interface Commit {
  changes: Record<string, FileChange>;
}

export class DiffError extends Error {}

export interface Chunk {
  origIndex: number;
  delLines: string[];
  insLines: string[];
}

export interface PatchAction {
  type: ActionType;
  newFile?: string | null;
  chunks: Chunk[];
  movePath?: string | null;
}

export interface Patch {
  actions: Record<string, PatchAction>;
}

type PrefixList = string[] | undefined;

type SectionResult = {
  context: string[];
  chunks: Chunk[];
  endIndex: number;
  eof: boolean;
};

function rstrip(value: string): string {
  return value.replace(/[\s\u00a0]+$/u, "");
}

function strip(value: string): string {
  return value.trim();
}

function splitLines(text: string): string[] {
  if (text === "") {
    return [];
  }
  const lines = text.split(/\r\n|[\n\r]/);
  if (lines.length > 0 && (text.endsWith("\n") || text.endsWith("\r"))) {
    lines.pop();
  }
  return lines;
}

class Parser {
  private readonly currentFiles: Record<string, string>;
  private readonly lines: string[];
  private index: number;
  private readonly patch: Patch;
  public fuzz: number;

  constructor(
    currentFiles: Record<string, string>,
    lines: string[],
    index = 0
  ) {
    this.currentFiles = currentFiles;
    this.lines = lines;
    this.index = index;
    this.patch = { actions: {} };
    this.fuzz = 0;
  }

  public parse(): Patch {
    while (!this.isDone(["*** End Patch"])) {
      const updatePath = this.readStr("*** Update File: ");
      if (updatePath) {
        if (this.patch.actions[updatePath]) {
          throw new DiffError(`Duplicate update for file: ${updatePath}`);
        }
        const moveTo = this.readStr("*** Move to: ");
        if (!(updatePath in this.currentFiles)) {
          throw new DiffError(
            `Update File Error - missing file: ${updatePath}`
          );
        }
        const action = this.parseUpdateFile(this.currentFiles[updatePath]);
        action.movePath = moveTo || null;
        this.patch.actions[updatePath] = action;
        continue;
      }

      const deletePath = this.readStr("*** Delete File: ");
      if (deletePath) {
        if (this.patch.actions[deletePath]) {
          throw new DiffError(`Duplicate delete for file: ${deletePath}`);
        }
        if (!(deletePath in this.currentFiles)) {
          throw new DiffError(
            `Delete File Error - missing file: ${deletePath}`
          );
        }
        this.patch.actions[deletePath] = {
          type: ActionType.DELETE,
          chunks: [],
        };
        continue;
      }

      const addPath = this.readStr("*** Add File: ");
      if (addPath) {
        if (this.patch.actions[addPath]) {
          throw new DiffError(`Duplicate add for file: ${addPath}`);
        }
        if (addPath in this.currentFiles) {
          throw new DiffError(
            `Add File Error - file already exists: ${addPath}`
          );
        }
        this.patch.actions[addPath] = this.parseAddFile();
        continue;
      }

      throw new DiffError(`Unknown line while parsing: ${this.curLine()}`);
    }

    if (!this.startsWith("*** End Patch")) {
      throw new DiffError("Missing *** End Patch sentinel");
    }
    this.index += 1;
    return this.patch;
  }

  private curLine(): string {
    if (this.index >= this.lines.length) {
      throw new DiffError("Unexpected end of input while parsing patch");
    }
    return this.lines[this.index];
  }

  public static norm(line: string): string {
    return line.replace(/\r$/u, "");
  }

  private isDone(prefixes: PrefixList = undefined): boolean {
    if (this.index >= this.lines.length) {
      return true;
    }
    if (prefixes && prefixes.length > 0) {
      const current = Parser.norm(this.curLine());
      if (prefixes.some((prefix) => current.startsWith(prefix))) {
        return true;
      }
    }
    return false;
  }

  private startsWith(prefix: string | string[]): boolean {
    const prefixes = Array.isArray(prefix) ? prefix : [prefix];
    const current = Parser.norm(this.curLine());
    return prefixes.some((value) => current.startsWith(value));
  }

  private readStr(prefix: string): string {
    if (prefix.length === 0) {
      throw new Error("readStr() requires a non-empty prefix");
    }
    if (this.index >= this.lines.length) {
      return "";
    }
    const current = Parser.norm(this.curLine());
    if (current.startsWith(prefix)) {
      const text = this.curLine().slice(prefix.length);
      this.index += 1;
      return text;
    }
    return "";
  }

  private readLine(): string {
    const line = this.curLine();
    this.index += 1;
    return line;
  }

  private parseUpdateFile(text: string): PatchAction {
    const action: PatchAction = { type: ActionType.UPDATE, chunks: [] };
    const lines = text.split("\n");
    let index = 0;

    while (
      !this.isDone([
        "*** End Patch",
        "*** Update File:",
        "*** Delete File:",
        "*** Add File:",
        "*** End of File",
      ])
    ) {
      const defStr = this.readStr("@@ ");
      let consumedSectionHeader = false;
      if (!defStr && Parser.norm(this.curLine()) === "@@") {
        this.readLine();
        consumedSectionHeader = true;
      }

      if (!defStr && !consumedSectionHeader && index !== 0) {
        throw new DiffError(
          `Invalid line in update section:\n${this.curLine()}`
        );
      }

      if (defStr.trim()) {
        let found = false;
        if (!lines.slice(0, index).includes(defStr)) {
          for (let i = index; i < lines.length; i += 1) {
            if (lines[i] === defStr) {
              index = i + 1;
              found = true;
              break;
            }
          }
        }
        if (
          !found &&
          !lines
            .slice(0, index)
            .map((s) => s.trim())
            .includes(defStr.trim())
        ) {
          for (let i = index; i < lines.length; i += 1) {
            if (lines[i].trim() === defStr.trim()) {
              index = i + 1;
              this.fuzz += 1;
              found = true;
              break;
            }
          }
        }
      }

      const section = peekNextSection(this.lines, this.index);
      const contextLines = section.context;
      const sectionChunks = section.chunks;
      const endIndex = section.endIndex;
      const eof = section.eof;

      const [newIndex, fuzz] = findContext(lines, contextLines, index, eof);
      if (newIndex === -1) {
        const ctxTxt = contextLines.join("\n");
        throw new DiffError(
          `Invalid ${eof ? "EOF " : ""}context at ${index}:\n${ctxTxt}`
        );
      }
      this.fuzz += fuzz;
      for (const chunk of sectionChunks) {
        action.chunks.push({
          origIndex: chunk.origIndex + newIndex,
          delLines: [...chunk.delLines],
          insLines: [...chunk.insLines],
        });
      }
      index = newIndex + contextLines.length;
      this.index = endIndex;
    }
    return action;
  }

  private parseAddFile(): PatchAction {
    const lines: string[] = [];
    while (
      !this.isDone([
        "*** End Patch",
        "*** Update File:",
        "*** Delete File:",
        "*** Add File:",
      ])
    ) {
      const raw = this.readLine();
      if (!raw.startsWith("+")) {
        throw new DiffError(`Invalid Add File line (missing '+'): ${raw}`);
      }
      lines.push(raw.slice(1));
    }
    return { type: ActionType.ADD, newFile: lines.join("\n"), chunks: [] };
  }
}

function findContextCore(
  lines: string[],
  context: string[],
  start: number
): [number, number] {
  if (context.length === 0) {
    return [start, 0];
  }

  for (let i = start; i <= lines.length - context.length; i += 1) {
    const candidate = lines.slice(i, i + context.length);
    if (candidate.every((value, idx) => value === context[idx])) {
      return [i, 0];
    }
  }
  for (let i = start; i <= lines.length - context.length; i += 1) {
    const candidate = lines.slice(i, i + context.length);
    if (
      candidate.every((value, idx) => rstrip(value) === rstrip(context[idx]))
    ) {
      return [i, 1];
    }
  }
  for (let i = start; i <= lines.length - context.length; i += 1) {
    const candidate = lines.slice(i, i + context.length);
    if (candidate.every((value, idx) => strip(value) === strip(context[idx]))) {
      return [i, 100];
    }
  }
  return [-1, 0];
}

function findContext(
  lines: string[],
  context: string[],
  start: number,
  eof: boolean
): [number, number] {
  if (context.length === 0) {
    return [start, 0];
  }
  if (eof) {
    const eofStart = Math.max(0, lines.length - context.length);
    const [newIndex, fuzz] = findContextCore(lines, context, eofStart);
    if (newIndex !== -1) {
      return [newIndex, fuzz];
    }
    const [retryIndex, retryFuzz] = findContextCore(lines, context, start);
    return [retryIndex, retryFuzz + 10_000];
  }
  return findContextCore(lines, context, start);
}

function peekNextSection(lines: string[], startIndex: number): SectionResult {
  const oldLines: string[] = [];
  let delLines: string[] = [];
  let insLines: string[] = [];
  const chunks: Chunk[] = [];
  let mode: "keep" | "add" | "delete" = "keep";
  const originalIndex = startIndex;
  let index = startIndex;

  while (index < lines.length) {
    const raw = lines[index];
    if (
      raw.startsWith("@@") ||
      raw.startsWith("*** End Patch") ||
      raw.startsWith("*** Update File:") ||
      raw.startsWith("*** Delete File:") ||
      raw.startsWith("*** Add File:") ||
      raw.startsWith("*** End of File")
    ) {
      break;
    }
    if (raw === "***") {
      break;
    }
    if (raw.startsWith("***")) {
      throw new DiffError(`Invalid Line: ${raw}`);
    }
    index += 1;
    const lastMode = mode;
    let line = raw;
    if (line === "") {
      line = " ";
    }
    const tag = line[0];
    if (tag === "+") {
      mode = "add";
    } else if (tag === "-") {
      mode = "delete";
    } else if (tag === " ") {
      mode = "keep";
    } else {
      throw new DiffError(`Invalid Line: ${raw}`);
    }
    line = line.slice(1);

    if (mode === "keep" && lastMode !== mode) {
      if (insLines.length > 0 || delLines.length > 0) {
        chunks.push({
          origIndex: oldLines.length - delLines.length,
          delLines: [...delLines],
          insLines: [...insLines],
        });
      }
      delLines = [];
      insLines = [];
    }

    if (mode === "delete") {
      delLines.push(line);
      oldLines.push(line);
    } else if (mode === "add") {
      insLines.push(line);
    } else {
      oldLines.push(line);
    }
  }

  if (insLines.length > 0 || delLines.length > 0) {
    chunks.push({
      origIndex: oldLines.length - delLines.length,
      delLines: [...delLines],
      insLines: [...insLines],
    });
  }

  if (index < lines.length && lines[index] === "*** End of File") {
    return { context: oldLines, chunks, endIndex: index + 1, eof: true };
  }

  if (index === originalIndex) {
    throw new DiffError("Nothing in this section");
  }

  return { context: oldLines, chunks, endIndex: index, eof: false };
}

function getUpdatedFile(
  text: string,
  action: PatchAction,
  filePath: string
): string {
  if (action.type !== ActionType.UPDATE) {
    throw new DiffError("_get_updated_file called with non-update action");
  }
  const originLines = text.split("\n");
  const resultLines: string[] = [];
  let originIndex = 0;

  for (const chunk of action.chunks) {
    if (chunk.origIndex > originLines.length) {
      throw new DiffError(
        `${filePath}: chunk.orig_index ${chunk.origIndex} exceeds file length`
      );
    }
    if (originIndex > chunk.origIndex) {
      throw new DiffError(
        `${filePath}: overlapping chunks at ${originIndex} > ${chunk.origIndex}`
      );
    }

    resultLines.push(...originLines.slice(originIndex, chunk.origIndex));
    originIndex = chunk.origIndex;

    resultLines.push(...chunk.insLines);
    originIndex += chunk.delLines.length;
  }

  resultLines.push(...originLines.slice(originIndex));
  return resultLines.join("\n");
}

export function patchToCommit(
  patch: Patch,
  orig: Record<string, string>
): Commit {
  const commit: Commit = { changes: {} };
  for (const [filePath, action] of Object.entries(patch.actions)) {
    if (action.type === ActionType.DELETE) {
      commit.changes[filePath] = {
        type: ActionType.DELETE,
        oldContent: orig[filePath],
      };
    } else if (action.type === ActionType.ADD) {
      if (action.newFile == null) {
        throw new DiffError("ADD action without file content");
      }
      commit.changes[filePath] = {
        type: ActionType.ADD,
        newContent: action.newFile,
      };
    } else if (action.type === ActionType.UPDATE) {
      const newContent = getUpdatedFile(orig[filePath], action, filePath);
      commit.changes[filePath] = {
        type: ActionType.UPDATE,
        oldContent: orig[filePath],
        newContent,
        movePath: action.movePath ?? null,
      };
    }
  }
  return commit;
}

export function textToPatch(
  text: string,
  orig: Record<string, string>
): [Patch, number] {
  const lines = splitLines(text);
  if (
    lines.length < 2 ||
    !Parser.norm(lines[0]).startsWith("*** Begin Patch") ||
    Parser.norm(lines[lines.length - 1]) !== "*** End Patch"
  ) {
    throw new DiffError("Invalid patch text - missing sentinels");
  }

  const parser = new Parser(orig, lines, 1);
  const patch = parser.parse();
  return [patch, parser.fuzz];
}

export function identifyFilesNeeded(text: string): string[] {
  const lines = splitLines(text);
  const updates = lines
    .filter((line) => line.startsWith("*** Update File: "))
    .map((line) => line.slice("*** Update File: ".length));
  const deletes = lines
    .filter((line) => line.startsWith("*** Delete File: "))
    .map((line) => line.slice("*** Delete File: ".length));
  return [...updates, ...deletes];
}

export function identifyFilesAdded(text: string): string[] {
  return splitLines(text)
    .filter((line) => line.startsWith("*** Add File: "))
    .map((line) => line.slice("*** Add File: ".length));
}

export function loadFiles(
  paths: string[],
  openFn: (filePath: string) => string
): Record<string, string> {
  return paths.reduce<Record<string, string>>((acc, filePath) => {
    acc[filePath] = openFn(filePath);
    return acc;
  }, {});
}

export function applyCommit(
  commit: Commit,
  writeFn: (filePath: string, content: string) => void,
  removeFn: (filePath: string) => void
): void {
  for (const [filePath, change] of Object.entries(commit.changes)) {
    if (change.type === ActionType.DELETE) {
      removeFn(filePath);
    } else if (change.type === ActionType.ADD) {
      if (change.newContent == null) {
        throw new DiffError(`ADD change for ${filePath} has no content`);
      }
      writeFn(filePath, change.newContent);
    } else if (change.type === ActionType.UPDATE) {
      if (change.newContent == null) {
        throw new DiffError(`UPDATE change for ${filePath} has no new content`);
      }
      const target = change.movePath ?? filePath;
      writeFn(target, change.newContent);
      if (change.movePath) {
        removeFn(filePath);
      }
    }
  }
}

export function processPatch(
  text: string,
  openFn: (filePath: string) => string,
  writeFn: (filePath: string, content: string) => void,
  removeFn: (filePath: string) => void
): string {
  if (!text.startsWith("*** Begin Patch")) {
    throw new DiffError("Patch text must start with *** Begin Patch");
  }
  const paths = identifyFilesNeeded(text);
  const orig = loadFiles(paths, openFn);
  const [patch] = textToPatch(text, orig);
  const commit = patchToCommit(patch, orig);
  applyCommit(commit, writeFn, removeFn);
  return "Done!";
}
